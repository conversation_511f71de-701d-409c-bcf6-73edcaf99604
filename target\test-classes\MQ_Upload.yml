# Environment-specific configurations
environments:
  DEV:
    mq_storage_root: "C:\\opt\\ecs\\storage\\mq"
    app_root: "C:\\Development\\TFS_SourceCode\\ECS\\SZ_GO\\Common\\Dev\\SFTPSend_Hao"
  PROD:
    mq_storage_root: "C:\\opt\\ecs\\storage\\mq\\prod"
    app_root: "C:\\opt\\ecs\\apps\\mqupload"
  TV_UAT:
    mq_storage_root: "/u/tsd/misc/mq_send/sent"
    app_root: "/u/MQUpload"
    RabbitMQEnv: "DEV"


# Producer Configuration (same for all environments)
producer:
  pds_dir:
    base: mq_storage_root
    relative: ""
  binary_format: true
  max_files_in_sending: 10
  waiting_for_write_ended: 100
  to_dir:
    base: mq_storage_root
    relative: "sent"
  db_msg_id_profile:
    base: app_root
    relative: "profiles\\pdsMsgId.pro"
  monitor_interval: 10

# Environment-specific RabbitMQ configurations
rabbitmq:
  DEV:
    host: "csf-md-rabitmq1.ad.shared"
    port: 5672
    username: "mqupload"
    password: "upload66"
    exchange: "ecs.direct"
    queue_config_path:
      base: app_root
      relative: "src\\test\\resources\\test-queue-config.csv"
  PROD:
    host: "csf-mp-talend03.ad.shared"
    port: 5672
    username: "mqupload"
    password: "upload66"
    exchange: "ecs.direct"
    queue_config_path:
      base: app_root
      relative: "config\\queue-config.csv"

table_schema:
  message_mappings_path:
    base: app_root
    relative: "src\\test\\resources\\mapper\\message_mappings.json"
  profile_base_path:
    base: app_root
    relative: "profiles\\generatedProfiles"

# Consumer Configuration
consumer:
  mybatis_config_path:
    base: app_root
    relative: "src\\test\\resources\\mybatis-config.xml"
  parser_class_name: "wdc.disk.ecs.apps.MQUpload.parser.AsciiMessageParser"
  mappings_path:
    base: app_root
    relative: "src\\test\\resources\\mapper\\message_mappings.json"
  profiles_path:
    base: app_root
    relative: "profiles\\generatedProfiles"
  queues:
    - name: "309"
      description: "Queue for 309 processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "318"
      description: "Queue for 318 processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "222"
      description: "Queue for 222 processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "223"
      description: "Queue for 223 processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "300"
      description: "Queue for TDS_SCAN processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "301"
      description: "Queue for TDS_CHANNEL_STAT processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "302"
      description: "Queue for TDS_HEAD_STAT processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "303"
      description: "Queue for TDS_GRADE processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "304"
      description: "Queue for TDS_CLUSTER processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "305"
      description: "Queue for TDS_CLUSTER_DATA processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "306"
      description: "Queue for TDS_SITEMARK_CLUS processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "310"
      description: "Queue for TDS_ENG processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "2"
      description: "Queue for GLDRSLT processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "6"
      description: "Queue for SECTRSLT processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "7"
      description: "Queue for TRCKRSLT processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "8"
      description: "Queue for BANDRSLT processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "11"
      description: "Queue for BATCHINFO processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "12"
      description: "Queue for TLOG processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "15"
      description: "Queue for MONDATA processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "21"
      description: "Queue for SH_GMONDATA processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "24"
      description: "Queue for MISCDATA processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "25"
      description: "Queue for GEOMRSLT processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "26"
      description: "Queue for DECOGAUGE processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "41"
      description: "Queue for GLDLOG processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "32"
      description: "Queue for MONRSLT processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "27"
      description: "Queue for BANDRSLT_STRING processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "42"
      description: "Queue for TESTLOG processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "31"
      description: "Queue for SOFTRSLT processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "33"
      description: "Queue for GAUGERUN processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "43"
      description: "Queue for HEADLOG processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "44"
      description: "Queue for GMONDATA processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "36"
      description: "Queue for TRCKRSLT processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "40"
      description: "Queue for SCNRINFO processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "10"
      description: "Queue for HEADLOG processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "18"
      description: "Queue for GMONDATA processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "19"
      description: "Queue for MONRSLT processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "20"
      description: "Queue for GAUGERUN processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "29"
      description: "Queue for GLDLOG processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "30"
      description: "Queue for TESTLOG processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "34"
      description: "Queue for GLDLOG processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "35"
      description: "Queue for TESTLOG processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "37"
      description: "Queue for GLDPARM processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "38"
      description: "Queue for MAGCBPRM processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "39"
      description: "Queue for MAGMBPRM processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    - name: "45"
      description: "Queue for SH_GMONDATA processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100

