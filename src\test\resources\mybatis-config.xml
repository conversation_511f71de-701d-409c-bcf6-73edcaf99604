<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>

    <settings>
        <!-- Enable SQL logging -->
        <setting name="logImpl" value="SLF4J"/>
        <setting name="logPrefix" value="MyBatis_SQL."/>
    </settings>

    <environments default="development">
        <environment id="development">
            <transactionManager type="JDBC"/>
            <dataSource type="POOLED">
                <property name="driver" value="com.ibm.db2.jcc.DB2Driver"/>
                <property name="url" value="***************************************************"/>
                <property name="username" value="ndutld"/>
                <property name="password" value="Itc@me123"/>
            </dataSource>
        </environment>
    </environments>
    
    <mappers>
         <package name="wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers"/>
    </mappers>
</configuration>