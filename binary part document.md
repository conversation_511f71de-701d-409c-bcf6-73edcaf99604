# 系统设计文档

本文件旨在说明当前系统中两个关键部分的设计：二进制解析器以及POJO和Mapper文件的自动生成脚本。

## 1. 二进制解析器 (Binary Parser)

二进制解析器负责将传入的二进制消息流转换为结构化的数据。

### 核心组件

- **`wdc.disk.ecs.apps.SFTPSend.binary.BinaryService`**: 这是处理二进制消息的核心服务类。
- **`.pro` 配置文件**: 每个消息类型都有一个对应的 `.pro` 文件，它定义了二进制消息的结构，包括每个字段的起始位置、长度和数据类型。

### 工作流程

1.  **加载配置文件**: `BinaryService` 首先会根据消息ID加载对应的 `.pro` 配置文件。这个文件包含了消息的元数据，是解析的依据。

2.  **解析消息 (`parseMsg` 方法)**:
    -   该方法遍历 `.pro` 文件中定义的每一个列（字段）。
    -   对于每个字段，它会根据配置文件中定义的 `StartIndex` (起始位置) 和 `BinaryLength` (二进制长度) 从原始的十六进制消息字符串中截取对应的部分。
    -   截取出的十六进制数据会被传递给 `parse` 方法进行进一步处理。

3.  **数据类型转换 (`parse` 方法)**:
    -   `parse` 方法根据字段的数据库类型（如 `SMALLINT`, `INTEGER`, `DOUBLE`, `TIMESTAMP`, `CHAR`）来调用相应的转换逻辑。
    -   实际的十六进制到具体数据类型的转换逻辑由 `wdc.disk.ecs.apps.SFTPSend.common.pds.conversion.ConvertorUtility` 类提供。例如，`ConvertHexBytesToShort` 和 `ConvertHexBytesToInteger` 等方法。

4.  **输出**:
    -   所有字段都被解析并转换为其相应的数据类型后，它们会被一个指定的分隔符拼接成一个字符串，以便后续处理。

### 特殊消息处理

- **打包消息**: `splitPackedMsgToArrayList` 方法用于处理包含多个消息的打包消息，将其拆分为单个消息列表。
- **单个消息**: `generatePdsStringForIndividualMsg` 方法用于处理独立的单个消息。

## 2. POJO 和 Mapper 生成器

为了将数据库中的数据映射到Java对象，系统提供了一个脚本来自动生成POJO（Plain Old Java Object）和MyBatis Mapper文件。

### 核心组件

- **`wdc.disk.ecs.apps.MQUpload.generator.ProfileAndMapperGenerator`**: 这是生成器的主类。
- **`.pro` 配置文件**: 与二进制解析器一样，生成器也使用 `.pro` 文件作为输入。这个文件提供了生成POJO属性和Mapper映射所需的全部元数据。
- **Velocity 模板引擎**: 生成器使用Velocity模板引擎来根据模板文件和 `.pro` 文件中的数据生成Java代码。

### 工作流程

1.  **配置生成任务**:
    -   在 `ProfileAndMapperGenerator.java` 的 `main` 方法中，通过一个 `TableInfo` 对象列表来定义需要生成代码的数据库表。
    -   `TableInfo` 对象包含了数据库的 `schema`, `tableName`, `tabCode`, 和 `messageId` 等信息。

2.  **执行生成 (`generateFromProfile` 方法)**:
    -   脚本会为 `TableInfo` 列表中的每个表执行生成操作。
    -   它首先会定位到该表对应的、已经生成好的增强型 `.pro` 配置文件（位于 `profiles/generatedProfiles/` 目录下）。
    -   然后，`PojoAndMapperGenerator` 会调用 `generateFromProfile` 方法。

3.  **代码生成**:
    -   此方法会读取 `.pro` 文件中的元数据（列名、数据类型、长度等）。
    -   它将这些元数据与预定义的Velocity模板相结合，为每个表生成一个POJO类（包含所有字段作为属性）和一个MyBatis Mapper接口（包含数据插入等操作）。

4.  **输出**:
    -   生成的 `* .java` 文件会被保存在 `src/main/java/wdc/disk/ecs/apps/MQUpload/` 路径下的 `model/generatedModels` 和 `mapper/generatedMappers` 目录中。

---

## 过程回顾

为了完成您的请求，我执行了以下步骤：

1.  **理解需求**: 分析了您需要一份关于二进制解析器和代码生成器的中文技术文档。
2.  **代码分析**:
    -   通过分析 `BinaryService.java`，我理解了二进制解析器如何利用 `.pro` 文件将十六进制字符串转换为结构化数据。
    -   通过重新审查 `ProfileAndMapperGenerator.java` 和之前的操作，我明确了POJO和Mapper文件是如何通过模板引擎和 `.pro` 文件自动生成的。
3.  **构建文档**: 我将分析得到的信息整理成一份结构清晰的Markdown文档，并按照您的要求使用中文编写。