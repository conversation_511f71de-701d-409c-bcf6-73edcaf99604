package wdc.disk.ecs.apps.MQUpload.producer;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import com.rabbitmq.client.Address;

import ibm.disk.profiles.DiskProfile;
import ibm.disk.profiles.ProfileException;
import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.MQUpload.model.RabbitMQMetadataLoader;
import wdc.disk.ecs.apps.MQUpload.model.RabbitMQProperties;
import wdc.disk.ecs.apps.MQUpload.processor.AsciiFileProcessor;
import wdc.disk.ecs.apps.MQUpload.processor.BinaryFileProcessor;
import wdc.disk.ecs.apps.MQUpload.processor.FileProcessingTemplate;
import wdc.disk.ecs.apps.MQUpload.processor.FileProcessor;
import wdc.disk.ecs.apps.MQUpload.schema.TableSchemaManager;
import wdc.disk.ecs.apps.MQUpload.service.ConfigurationLoader;
import wdc.disk.ecs.apps.MQUpload.thread.HousekeepingThread;
import wdc.disk.ecs.apps.MQUpload.util.RabbitMQSetupUtil;
import wdc.disk.ecs.apps.MQUpload.service.FileOperationService;
import wdc.disk.ecs.apps.MQUpload.service.RabbitMQProducerService;

/**
 * Main class for the MQUpload application that monitors directories and uploads files to RabbitMQ.
 * This class will do the file monitoring, processing, and uploading operations.
 *
 */
public class MQUpload implements Runnable {
    private final LogStream log;
    private final String configProfile;
    private ConfigurationLoader configLoader;  
    private FileOperationService fileMonitorService;
    private RabbitMQProducerService rabbitMQService;
    private boolean isRunning;
    private FileProcessingTemplate fileProcessingTemplate;
    private ScheduledExecutorService scheduler;
    private ScheduledExecutorService housekeepingScheduler;

    public MQUpload(String configProfile, LogStream log) {
        this.configProfile = configProfile;
        this.log = log;
        this.isRunning = false;
    }

    public void initialize() throws Exception {
        // Load configuration
        configLoader = new ConfigurationLoader(log, configProfile);
        configLoader.loadConfiguration();

        // Initialize queues if needed
        initializeQueuesIfNeeded();

        // Initialize services using producer properties
        fileMonitorService = new FileOperationService(
            configLoader.getProducerProperties().getPdsDir(), 
            log, 
            configLoader.getProducerProperties().getWaitingForWriteEnded(),
            configLoader.getProducerProperties().getMaxFilesInSending()
        );
        
        rabbitMQService = new RabbitMQProducerService(configLoader.getRabbitMQProperties(), log);

        // Initialize profiles first
        DiskProfile dbMsgIDProfile = null;
        if (!configLoader.getProducerProperties().isBinaryFormat()) {  
            try {
                dbMsgIDProfile = new DiskProfile(
                    configLoader.getProducerProperties().getDbMsgIdProfile(),
                    "wdc.disk.ecs.apps.SFTPSend.profile.DBMsgIDRecord",
                    DiskProfile.KEEP_ONE,
                    log
                );
                log.write(LogStream.INFORMATION_MESSAGE, 
                    "Initialized DB Message ID Profile: " + configLoader.getProducerProperties().getDbMsgIdProfile());
            } catch (ProfileException e) {
                log.write(LogStream.ERROR_MESSAGE, 
                    "Failed to initialize DB Message ID Profile: " + e.getMessage());
                throw e;
            }
        }

        // Initialize processor based on configuration
        FileProcessor processor = configLoader.getProducerProperties().isBinaryFormat()
            ? new BinaryFileProcessor(log, TableSchemaManager.getInstance(
                configLoader.getTableSchemaProperties().getMappingsPath(), 
                configLoader.getTableSchemaProperties().getProfilesPath()))
            : new AsciiFileProcessor(log, dbMsgIDProfile, configLoader.getProducerProperties().getDbMsgIdProfile());

        // Initialize template
        fileProcessingTemplate = new FileProcessingTemplate(log, processor, rabbitMQService, fileMonitorService);

        // Create scheduler
        scheduler = Executors.newScheduledThreadPool(1);

        // Initialize housekeeping task if enabled
        if (configLoader.getProducerProperties().isHousekeepingEnabled()) {
            log.write(LogStream.INFORMATION_MESSAGE, "Housekeeping is enabled.");
            HousekeepingThread housekeepingThread = new HousekeepingThread(
                configLoader.getProducerProperties().getPdsDir(),
                configLoader.getProducerProperties().getHousekeepingDirectories(),
                configLoader.getProducerProperties().getHousekeepingRetentionDays(),
                log
            );
            housekeepingScheduler = Executors.newScheduledThreadPool(1);
            housekeepingScheduler.scheduleAtFixedRate(housekeepingThread, 1, configLoader.getProducerProperties().getHousekeepingIntervalHours(), TimeUnit.HOURS);
        }
    }

    private void initializeQueuesIfNeeded() throws Exception {
        log.write(LogStream.INFORMATION_MESSAGE, "Checking and initializing RabbitMQ queues if needed...");
        
        Connection connection = null;
        Channel channel = null;
        
        try {
            // Create connection factory
            ConnectionFactory connectionFactory = new ConnectionFactory();
            RabbitMQProperties mqProps = configLoader.getRabbitMQProperties();
            
            // Create Address array from hosts
            Address[] addresses = new Address[mqProps.getRabbitMQHosts().length];
            for (int i = 0; i < mqProps.getRabbitMQHosts().length; i++) {
                addresses[i] = new Address(mqProps.getRabbitMQHosts()[i], mqProps.getRabbitMQPort());
            }
            
            connectionFactory.setUsername(mqProps.getRabbitMQUsername());
            connectionFactory.setPassword(mqProps.getRabbitMQPassword());
            connectionFactory.setAutomaticRecoveryEnabled(true);
            connectionFactory.setNetworkRecoveryInterval(10000);
            
            // Create connection and channel
            connection = connectionFactory.newConnection(addresses);
            channel = connection.createChannel();
            
            // Setup queues using the utility
            RabbitMQSetupUtil.setupQueues(channel, 
                configLoader.getConsumerProperties(),
                RabbitMQMetadataLoader.loadFromCsv(configLoader.getRabbitMQProperties().getRabbitMQQueueConfigPath()),
                log
            );
            
            log.write(LogStream.INFORMATION_MESSAGE, "Queue initialization completed successfully");
            
        } catch (Exception e) {
            log.write(LogStream.ERROR_MESSAGE, "Failed to initialize queues: " + e.getMessage());
            log.writeExceptionStack(e);
            throw e;
        } finally {
            // Clean up resources
            if (channel != null && channel.isOpen()) {
                try {
                    channel.close();
                } catch (Exception e) {
                    log.write(LogStream.WARNING_MESSAGE, "Error closing channel: " + e.getMessage());
                }
            }
            if (connection != null && connection.isOpen()) {
                try {
                    connection.close();
                } catch (Exception e) {
                    log.write(LogStream.WARNING_MESSAGE, "Error closing connection: " + e.getMessage());
                }
            }
        }
    }

    public void start() {
        if (isRunning) {
            log.write(LogStream.WARNING_MESSAGE, "MQUpload is already running");
            return;
        }

        isRunning = true;
        scheduler.scheduleAtFixedRate(this, 0, 
            configLoader.getProducerProperties().getMonitorInterval(), TimeUnit.SECONDS);
        log.write(LogStream.INFORMATION_MESSAGE, 
            "MQUpload started. Monitoring directory: " + configLoader.getProducerProperties().getPdsDir());

    }

    /**
     * Stops the file monitoring and upload process.
     */
    public void stop() {
        if (!isRunning) {
            return;
        }

        isRunning = false;
        scheduler.shutdown();
        if (housekeepingScheduler != null) {
            housekeepingScheduler.shutdown();
        }
        try {
            scheduler.awaitTermination(60, TimeUnit.SECONDS);
            if (housekeepingScheduler != null) {
                housekeepingScheduler.awaitTermination(60, TimeUnit.SECONDS);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.write(LogStream.ERROR_MESSAGE, "Error while stopping MQUpload: " + e.getMessage());
        }

        rabbitMQService.close();
        log.write(LogStream.INFORMATION_MESSAGE, "MQUpload stopped");
    }

    @Override
    public void run() {
        if (!isRunning) {
            return;
        }

        try {
            fileProcessingTemplate.processAndUploadFiles();
        } catch (Exception e) {
            log.write(LogStream.ERROR_MESSAGE, "Error in run cycle: " + e.getMessage());
        }
    }

    /**
     * Gets the current configuration properties.
     *
     * @return The current ConfigurationProperties instance
     */
    // Remove getConfig() and setConfig() methods as they're no longer needed
    
    // Add getter for ConfigurationLoader if needed
    public ConfigurationLoader getConfigLoader() {
        return configLoader;
    }
    
    /**
     * Checks if the application is currently running.
     *
     * @return true if the application is running, false otherwise
     */
    public boolean isRunning() {
        return isRunning;
    }
    
    public static void main(String[] args) {
        if (args.length != 2) {
            System.err.println("Usage: MQUpload <configProfile> <LOG_FILE>");
            System.exit(1);
        }

        String configProfile = args[0];
        String logfilePath = args[1];
        LogStream log = new LogStream(logfilePath);

        try {
            MQUpload mqUpload = new MQUpload(configProfile, log);
            
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                log.write(LogStream.INFORMATION_MESSAGE, "Shutting down MQUpload...");
                mqUpload.stop();
            }));

            mqUpload.initialize();
            mqUpload.start();

            // Keep the main thread alive
            while (mqUpload.isRunning()) {
                Thread.sleep(1000);
            }
        } catch (Exception e) {
            log.write(LogStream.ERROR_MESSAGE, "Application error: " + e.getMessage());
        	log.writeExceptionStack(e);
            System.exit(1);
        }
    }
}

