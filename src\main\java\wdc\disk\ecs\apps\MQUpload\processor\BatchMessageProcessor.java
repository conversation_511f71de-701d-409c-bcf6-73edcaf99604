package wdc.disk.ecs.apps.MQUpload.processor;


import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.DeliverCallback;
import com.rabbitmq.client.Delivery;
import wdc.disk.ecs.apps.MQUpload.model.QueueMetadataEntry;
import wdc.disk.ecs.apps.MQUpload.model.ConsumerProperties;
import java.util.ArrayList;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.MQUpload.exception.MessageProcessingException;
import wdc.disk.ecs.apps.MQUpload.mapper.MessageDataMapper;
import wdc.disk.ecs.apps.MQUpload.parser.MessageParser;
import wdc.disk.ecs.apps.MQUpload.parser.ParseResult;
import wdc.disk.ecs.apps.MQUpload.schema.TableSchemaManager;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;


public class BatchMessageProcessor {
    private final MessageParser messageParser;
    private final MessageDataMapper dataMapper;
    private final SqlSessionFactory sqlSessionFactory;
    private final LogStream log;
    private final TableSchemaManager schemaManager;
    private final String basePackage = "wdc.disk.ecs.apps.MQUpload";
    private final BlockingQueue<Delivery> localQueue = new LinkedBlockingQueue<>();
    private final int BATCH_SIZE = 10;
    private final int QUEUE_TIMEOUT_MS = 1000;
    private volatile boolean isRunning = true;
    private final AtomicLong processedMessageCount = new AtomicLong(0);
    private final AtomicLong queueSize = new AtomicLong(0);
    private final Connection connection;
    private final String messageId;

    private String tabCode = null;
    private Class<?> pojoClass = null;
    private Object mapper = null;
    private static final int DEFAULT_PREFETCH_COUNT = 100;
    private Channel channel;

    public BatchMessageProcessor(MessageParser messageParser, 
                               MessageDataMapper dataMapper,
                               SqlSessionFactory sqlSessionFactory,
                               LogStream log,
                               String queueName,
                               TableSchemaManager schemaManager,
                               Connection connection,
                               String messageId) {
        this.messageParser = messageParser;
        this.dataMapper = dataMapper;
        this.sqlSessionFactory = sqlSessionFactory;
        this.log = log;
        this.schemaManager = schemaManager;
        this.connection = connection;
        this.messageId = messageId;
    }

    /**
     * Processes a batch of RabbitMQ message deliveries.
     * This method handles the parsing, mapping, and database insertion of messages,
     * as well as error handling and acknowledgment.
     *
     * @param deliveries A list of RabbitMQ Delivery objects to process
     * @param channel The RabbitMQ channel for acknowledging messages
     */
    public void processDeliveryListOneByOne(List<Delivery> deliveries, Channel channel) {
        long startTime = System.currentTimeMillis();
        int processedCount = 0;
        int failedCount = 0;
        
        log.write(LogStream.INFORMATION_MESSAGE, 
            String.format("Starting to process delivery batch of %d messages for %s", deliveries.size(), tabCode));

        try {

            try (SqlSession session = sqlSessionFactory.openSession()) {
                for (Delivery delivery : deliveries) {
                    long messageStartTime = System.currentTimeMillis();
                    String message = new String(delivery.getBody(), StandardCharsets.UTF_8);
                    
                    ParseResult result = messageParser.parse(message);
                    if (result == null || result.getParsedFields() == null) {
                        handleFailedMessage(channel, delivery, "Message parsing failed", null);
                        failedCount++;
                        continue;
                    }

                    if (tabCode == null) {
                        tabCode = result.getTabCode();
                        long initStartTime = System.currentTimeMillis();
                        try {
                            pojoClass = Class.forName(String.format("%s.model.generatedModels.%s", basePackage, tabCode));
                            mapper = getMapperForTabCode(session, tabCode);
                            log.write(LogStream.INFORMATION_MESSAGE, 
                                String.format("Initialized mapper for tabCode %s in %dms", 
                                    tabCode, System.currentTimeMillis() - initStartTime));
                        } catch (ClassNotFoundException e) {
                            handleFailedMessage(channel, delivery, 
                                String.format("POJO class not found for tabCode: %s", tabCode), e);
                            failedCount++;
                            continue;
                        }

                        if (mapper == null) {
                            handleFailedMessage(channel, delivery, 
                                String.format("No mapper found for tabCode: %s", tabCode), null);
                            failedCount++;
                            continue;
                        }
                    }

                    try {
                        long mappingStartTime = System.currentTimeMillis();
                        Object pojoInstance = dataMapper.mapToPojo(result, pojoClass);
                        long mappingTime = System.currentTimeMillis() - mappingStartTime;
                        
                        long dbStartTime = System.currentTimeMillis();
                        invokeMapperMethod(mapper, pojoInstance);
                        session.commit();
                        
                        // Acknowledge successful processing
                        channel.basicAck(delivery.getEnvelope().getDeliveryTag(), false);
                        
                        processedCount++;
                        long messageProcessTime = System.currentTimeMillis() - messageStartTime;
                        
                        log.write(LogStream.INFORMATION_MESSAGE, 
                            String.format("%s Message processing completed in %dms (Mapping: %dms, DB: %dms)", 
                                tabCode,
                                messageProcessTime, mappingTime, 
                                System.currentTimeMillis() - dbStartTime));
                        
                    } catch (Exception e) {
                        session.rollback();
                        handleFailedMessage(channel, delivery, 
                            String.format("Error processing message with tabCode %s", tabCode), e);
                        failedCount++;
                    }
                }
            }
            
            long totalTime = System.currentTimeMillis() - startTime;
            log.write(LogStream.INFORMATION_MESSAGE, 
                String.format("Batch processing completed for %s. Total time: %dms, Processed: %d, Failed: %d", 
                    tabCode,
                    totalTime, processedCount, failedCount));
                
        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.write(LogStream.ERROR_MESSAGE, 
                String.format("Batch processing failed after %dms. Processed: %d, Failed: %d. Error: %s", 
                    totalTime, processedCount, failedCount, e.getMessage()));
            log.writeExceptionStack(e);
            throw new RuntimeException("Fatal error in batch processing", e);
        }
    }

    private Object getMapperForTabCode(SqlSession session, String tabCode) {
        try {
            Class<?> mapperClass = schemaManager.getMapperClassForTabCode(tabCode);
            return session.getMapper(mapperClass);
        } catch (ClassNotFoundException e) {
            throw new IllegalArgumentException("No mapper found for tabCode: " + tabCode, e);
        }
    }

    private void invokeMapperMethod(Object mapper, Object pojoInstance) {
        schemaManager.invokeMapperMethod(mapper, pojoInstance);
    }

    private void handleFailedMessage(Channel channel, Delivery delivery, String errorMessage, Exception e) {
        try {
            if (channel != null && channel.isOpen()) {
                // Determine if error is recoverable - default to true for most errors
                boolean isRecoverable = true;
                if (e != null) {
                    // Non-recoverable cases: class not found, mapping errors, validation errors
                    isRecoverable = !(e instanceof ClassNotFoundException || 
                                    e instanceof IllegalArgumentException ||
                                    e instanceof MessageProcessingException);
                }
                
                // For non-recoverable errors, reject without requeue to send to DLQ
                if (!isRecoverable) {
                    // basicReject with requeue=false will send to DLQ
                    channel.basicReject(delivery.getEnvelope().getDeliveryTag(), false);
                    log.write(LogStream.ERROR_MESSAGE, 
                        String.format("Message processing failed: %s. Message sent to dead letter queue", 
                            errorMessage));
                } else {
                    // For recoverable errors, NACK with requeue
                    channel.basicNack(delivery.getEnvelope().getDeliveryTag(), false, true);
                    log.write(LogStream.ERROR_MESSAGE, 
                        String.format("Message processing failed: %s. Message requeued", 
                            errorMessage));
                }
                
                if (e != null) {
                    log.writeExceptionStack(e);
                }
            } else {
                log.write(LogStream.ERROR_MESSAGE, "Channel is closed, cannot NACK message");
                throw new IOException("RabbitMQ channel is closed");
            }
        } catch (IOException ioException) {
            log.write(LogStream.ERROR_MESSAGE, 
                String.format("Error handling failed message: %s", ioException.getMessage()));
            throw new RuntimeException("Failed to handle message failure", ioException);
        }
    }

    /**
     * Processes multiple RabbitMQ messages in a single database transaction for improved performance.
     * This method handles bulk database operations using MyBatis BATCH executor.
     *
     * @param deliveries List of RabbitMQ Delivery objects to process in bulk
     * @param channel RabbitMQ channel for message acknowledgment
     * @throws MessageProcessingException if batch processing fails
     * @throws IllegalArgumentException if deliveries is null or empty
     * 
     * Implementation Notes:
     * - All messages must have the same tabCode for batch processing
     * - Uses a single database transaction for all messages
     * - Transaction timeout is controlled by database settings
     * - Falls back to individual message processing on batch failure
     * - Channel state is managed to prevent resource leaks
     */
    public void processBatchDeliveriesBulkTransaction(List<Delivery> deliveries) {
        if (deliveries == null || deliveries.isEmpty()) {
            throw new IllegalArgumentException("Deliveries list cannot be null or empty");
        }

        long startTime = System.currentTimeMillis();
        int processedCount = 0;
        int failedCount = 0;
        boolean channelNeedsRecovery = false;
        long totalProcessingTime = 0;
        
        log.write(LogStream.INFORMATION_MESSAGE, 
            String.format("Starting bulk transaction batch processing of %d messages ", deliveries.size()));

        SqlSession session = null;
        try {
            // Use BATCH executor with auto-commit disabled
            session = sqlSessionFactory.openSession(ExecutorType.BATCH, false);

            tabCode = schemaManager.getTabCodeFromMessageId(messageId);
            if(tabCode == null) {
                throw new IllegalArgumentException("No tabCode found for message ID: " + messageId);
            }
            log.write(LogStream.INFORMATION_MESSAGE, "Processing messages for tabCode: " + tabCode);
            pojoClass = initializePojoClass(tabCode);
            log.write(LogStream.INFORMATION_MESSAGE, "Initialized POJO class: " + pojoClass.getSimpleName());
            mapper = getMapperForTabCode(session, tabCode);
            log.write(LogStream.INFORMATION_MESSAGE, "Initialized mapper for tabCode: " + tabCode);

          

            // Process all messages in batch
            for (Delivery delivery : deliveries) {
                long messageStartTime = System.currentTimeMillis();
                try {
                    ParseResult result = validateAndParseMessage(delivery);
                    
                    // Ensure all messages have same tabCode
                    if (!tabCode.equals(result.getTabCode())) {
                        throw new MessageProcessingException(
                            String.format("Mixed tabCodes in batch. Expected: %s, Found: %s", 
                            tabCode, result.getTabCode()),
                            MessageProcessingException.ErrorType.VALIDATION_ERROR
                        );
                    }

                    Object pojoInstance = dataMapper.mapToPojo(result, pojoClass);
                    invokeMapperMethod(mapper, pojoInstance);
                    
                    // Flush every 100 records to avoid memory issues
                    if (processedCount > 0 && processedCount % 100 == 0) {
                        session.flushStatements();
                    }
                    
                    processedCount++;
                    
                    long messageProcessingTime = System.currentTimeMillis() - messageStartTime;
                    totalProcessingTime += messageProcessingTime;
                    log.write(LogStream.INFORMATION_MESSAGE, 
                        String.format("%s Message %d processed in %d ms", tabCode, processedCount, messageProcessingTime));
                } catch (Exception e) {
                    failedCount++;
                    String errorMsg = String.format("Error processing message %d in batch", processedCount + failedCount);
                    log.write(LogStream.ERROR_MESSAGE, errorMsg);
                    log.writeExceptionStack(e);
                    throw new MessageProcessingException(errorMsg, 
                        MessageProcessingException.ErrorType.BUSINESS_LOGIC_ERROR, e);
                }
            }

            // Final flush before commit
            session.flushStatements();
            
            // Try to commit in smaller batches if the batch size is large
            try {
                commitBatchAndAcknowledge(session, channel, deliveries, processedCount);
            } catch (Exception e) {
                log.write(LogStream.ERROR_MESSAGE, "Batch commit failed, attempting individual commits");
                // If batch commit fails, try to commit one by one
                for (int i = 0; i < deliveries.size(); i++) {
                    try {
                        session.commit();
                        channel.basicAck(deliveries.get(i).getEnvelope().getDeliveryTag(), false);
                    } catch (Exception innerE) {
                        log.write(LogStream.ERROR_MESSAGE, 
                            String.format("Failed to commit message %d: %s", i, innerE.getMessage()));
                        throw innerE;
                    }
                }
            }

        } catch (Exception e) {
            handleBatchFailure(session, e, deliveries, channel);
            channelNeedsRecovery = true;
        } finally {
            long totalTime = System.currentTimeMillis() - startTime;
            cleanupResources(session, channel, channelNeedsRecovery, startTime, processedCount, failedCount);
            if (processedCount > 0) {
                log.write(LogStream.INFORMATION_MESSAGE, 
                    String.format("Total batch processing time for %s: %d ms, Average time per message: %d ms", 
                        tabCode, totalTime, 
                        totalProcessingTime / processedCount));
            }
        }
    }

    /**
     * Validates and parses a RabbitMQ message.
     * @throws MessageProcessingException if parsing fails
     */
    private ParseResult validateAndParseMessage(Delivery delivery) throws MessageProcessingException {
        String message = new String(delivery.getBody(), StandardCharsets.UTF_8);
        ParseResult result = messageParser.parse(message);
        if (result == null || result.getParsedFields() == null) {
            throw new MessageProcessingException("Message parsing failed", 
                MessageProcessingException.ErrorType.VALIDATION_ERROR);
        }
        return result;
    }

    /**
     * Initializes the POJO class for the given tabCode.
     * @throws MessageProcessingException if class initialization fails
     */
    private Class<?> initializePojoClass(String tabCode) throws MessageProcessingException {
        try {
            return Class.forName(String.format("%s.model.generatedModels.%s", basePackage, tabCode));
        } catch (ClassNotFoundException e) {
            throw new MessageProcessingException(
                String.format("Failed to initialize POJO class for tabCode: %s", tabCode),
                MessageProcessingException.ErrorType.VALIDATION_ERROR, e);
        }
    }

    /**
     * Commits the batch transaction and acknowledges messages.
     * @throws MessageProcessingException if commit or acknowledge fails
     */
    private void commitBatchAndAcknowledge(SqlSession session, Channel channel, 
            List<Delivery> deliveries, int processedCount) throws MessageProcessingException {
        try {
            session.commit();
            
            // Acknowledge successful processing
            for (Delivery delivery : deliveries) {
                channel.basicAck(delivery.getEnvelope().getDeliveryTag(), false);
            }

            log.write(LogStream.INFORMATION_MESSAGE, 
                String.format("Bulk transaction committed. Processed: %d messages", processedCount));
        } catch (Exception e) {
            if (session != null) {
                session.rollback();
            }
            throw new MessageProcessingException("Failed to commit batch transaction", 
                MessageProcessingException.ErrorType.DATABASE_ERROR, e);
        }
    }

    /**
     * Handles batch processing failure by rolling back and falling back to individual processing.
     */
    private void handleBatchFailure(SqlSession session, Exception e, List<Delivery> deliveries, Channel channel) {
        if (session != null) {
            try {
                session.rollback();
            } catch (Exception rollbackEx) {
                log.write(LogStream.ERROR_MESSAGE, "Failed to rollback batch transaction");
                log.writeExceptionStack(rollbackEx);
            }
        }
        
        log.write(LogStream.ERROR_MESSAGE, "Bulk transaction failed, falling back to individual processing");
        log.writeExceptionStack(e);
        
        // Attempt individual processing
        processDeliveryListOneByOne(deliveries, channel);
    }

    /**
     * Cleans up resources and logs final status.
     */
    private void cleanupResources(SqlSession session, Channel channel, boolean channelNeedsRecovery,
            long startTime, int processedCount, int failedCount) {
        try {
            if (session != null) {
                session.close();
            }
            
            // if (channelNeedsRecovery && channel != null && channel.isOpen()) {
            //     try {
            //         channel.close();
            //     } catch (Exception e) {
            //         log.write(LogStream.ERROR_MESSAGE, "Failed to close RabbitMQ channel");
            //         log.writeExceptionStack(e);
            //     }
            // }
        } finally {
            long totalTime = System.currentTimeMillis() - startTime;
            log.write(LogStream.INFORMATION_MESSAGE, 
                String.format("Bulk transaction processing completed in %dms. Processed: %d, Failed: %d", 
                    totalTime, processedCount, failedCount));
        }
    }

    public void startConsuming(QueueMetadataEntry queueMetadata, ConsumerProperties.QueueConfig consumerQueueConfig)
            throws Exception {
        log.write(LogStream.INFORMATION_MESSAGE, "Starting to consume from queue: " + queueMetadata.getName());
        
        channel = getOrCreateChannel();
        log.write(LogStream.INFORMATION_MESSAGE, "Starting Channel number: " + channel.getChannelNumber() + 
            ", Thread name: " + Thread.currentThread().getName());
        
        int prefetchCount = consumerQueueConfig.getPrefetchCount() > 0 ? 
                consumerQueueConfig.getPrefetchCount() : DEFAULT_PREFETCH_COUNT;
        channel.basicQos(prefetchCount);
        
        Thread processorThread = new Thread(() -> {
            List<Delivery> batch = new ArrayList<>();
            long lastProcessTime = System.currentTimeMillis();

            while (isRunning) {
                try {
                
                    // Poll the local queue for a new delivery, waiting up to QUEUE_TIMEOUT_MS milliseconds
                    // This method returns null if the waiting time elapses before an element is available
                    // Using poll() instead of take() allows us to implement a timeout mechanism
                    Delivery delivery = localQueue.poll(QUEUE_TIMEOUT_MS, TimeUnit.MILLISECONDS);
                    long currentTime = System.currentTimeMillis();
                    
                    if (delivery != null) {
                        // Add the delivery to the batch and update the queue size
                        batch.add(delivery);
                        queueSize.decrementAndGet();
                        
                        // Try to fill batch
                        // This method attempts to drain elements from the localQueue into the batch
                        // It will transfer up to (BATCH_SIZE - batch.size()) elements
                        // The method returns the number of elements actually transferred
                        // This is an efficient way to fill the batch without multiple individual operations
                        int drained = localQueue.drainTo(batch, BATCH_SIZE - batch.size());
                        queueSize.addAndGet(-drained);
                    }

                    // Process if batch is full or timeout reached
                    boolean timeoutReached = currentTime - lastProcessTime >= QUEUE_TIMEOUT_MS;
                    log.write(LogStream.DEBUG_MESSAGE, 
                        String.format("Time since last process: %d ms, Timeout reached: %b", 
                            currentTime - lastProcessTime, timeoutReached));
                    log.write(LogStream.DEBUG_MESSAGE, "batch size: " + batch.size());
                    if (batch.size() >= BATCH_SIZE || (timeoutReached && !batch.isEmpty())) {
                        try {
                            // Check channel health before processing
                            if (channel == null || !channel.isOpen()) {
                                channel = getOrCreateChannel();
                                log.write(LogStream.WARNING_MESSAGE, "Channel recovered before batch processing");
                            }
                            
                            processBatchDeliveriesBulkTransaction(batch);
                            processedMessageCount.addAndGet(batch.size());
                            
                            log.write(LogStream.INFORMATION_MESSAGE, 
                                String.format("Processed batch of %d messages. Total processed: %d, Queue size: %d", 
                                    batch.size(), processedMessageCount.get(), queueSize.get()));
                        } catch (Exception e) {
                            log.write(LogStream.ERROR_MESSAGE, "Error processing batch: " + e.getMessage());
                            log.writeExceptionStack(e);
                            
                            // Reset channel on error
                            try {
                                if (channel != null && channel.isOpen()) {
                                    channel.close();
                                }
                                channel = getOrCreateChannel();
                                log.write(LogStream.WARNING_MESSAGE, "Channel reset after processing error");
                            } catch (Exception channelEx) {
                                log.write(LogStream.ERROR_MESSAGE, "Failed to reset channel: " + channelEx.getMessage());
                            }
                        } finally {
                            batch.clear();
                            lastProcessTime = currentTime;
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.write(LogStream.ERROR_MESSAGE, "Processor thread interrupted");
                    break;
                } catch (Exception e) {
                    log.write(LogStream.ERROR_MESSAGE, "Error in processor thread: " + e.getMessage());
                    log.writeExceptionStack(e);
                    // Add delay to prevent tight error loop
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e1) {
                        e1.printStackTrace();
                    }
                }
            }
            
            // Process remaining messages before shutdown
            if (!batch.isEmpty()) {
                try {
                    processBatchDeliveriesBulkTransaction(batch);
                } catch (Exception e) {
                    log.write(LogStream.ERROR_MESSAGE, "Error processing final batch: " + e.getMessage());
                }
            }
        }, "BatchProcessor-" + queueMetadata.getName());
        
        processorThread.setDaemon(true);
        processorThread.start();

        // Setup RabbitMQ consumer
        DeliverCallback deliverCallback = (consumerTag, delivery) -> {
            try {
                localQueue.put(delivery);
                queueSize.incrementAndGet();
                
                // Log if queue getting too large
                long currentSize = queueSize.get();
                log.write("currentSize: " + currentSize);
                if (currentSize > BATCH_SIZE * 10) {
                    log.write(LogStream.WARNING_MESSAGE, 
                        String.format("Local queue size growing large: %d messages", currentSize));
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.write(LogStream.ERROR_MESSAGE, "Interrupted while adding to local queue");
            }
        };

        channel.basicConsume(queueMetadata.getName(), false, deliverCallback,
                tag -> log.write(LogStream.WARNING_MESSAGE, "Consumer cancelled by broker: " + tag));
    }

    private Channel getOrCreateChannel() throws IOException {
        if (channel == null || !channel.isOpen()) {
            log.write(LogStream.WARNING_MESSAGE, "Creating new channel for thread: " + Thread.currentThread().getName());
            channel = connection.createChannel();
            channel.addShutdownListener(cause -> {
                if (!cause.isInitiatedByApplication()) {
                    log.write(LogStream.ERROR_MESSAGE, 
                        "Channel was closed unexpectedly: " + cause.getReason());
                }
            });
        }
        return channel;
    }

   

}





