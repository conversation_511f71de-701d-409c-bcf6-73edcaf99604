{"messageProfiles": [{"tabCode": "LID", "mqMessageId": "309", "msgType": "ASCII", "tableName": "LID"}, {"tabCode": "FTPRAW", "mqMessageId": "222", "msgType": "ASCII", "tableName": "FTP_RAW"}, {"tabCode": "FTPSUM", "mqMessageId": "223", "msgType": "ASCII", "tableName": "FTP_SUM"}, {"tabCode": "WASHCANDELA", "mqMessageId": "309", "msgType": "ASCII", "tableName": "WASHCANDELA"}, {"tabCode": "TDSCHANNEL", "mqMessageId": "301", "msgType": "ASCII", "tableName": "TDS_CHANNEL_STAT"}, {"tabCode": "TDSCLUSTER", "mqMessageId": "304", "msgType": "ASCII", "tableName": "TDS_CLUSTER"}, {"tabCode": "TDSCLUSDATA", "mqMessageId": "305", "msgType": "ASCII", "tableName": "TDS_CLUSTER_DATA"}, {"tabCode": "TDSENG", "mqMessageId": "310", "msgType": "ASCII", "tableName": "TDS_Eng"}, {"tabCode": "TDSGRADE", "mqMessageId": "303", "msgType": "ASCII", "tableName": "TDS_GRADE"}, {"tabCode": "TDSHEADSTAT", "mqMessageId": "302", "msgType": "ASCII", "tableName": "TDS_HEAD_STAT"}, {"tabCode": "TDSSCAN", "mqMessageId": "300", "msgType": "ASCII", "tableName": "TDS_SCAN"}, {"tabCode": "TDSSITEMARK", "mqMessageId": "306", "msgType": "ASCII", "tableName": "TDS_SITEMARK_CLUS"}, {"tabCode": "GLDRSLT_2", "mqMessageId": "2", "msgType": "Binary", "tableName": "GLDRSLT"}, {"tabCode": "CLIPRSLT_3", "mqMessageId": "3", "msgType": "Binary", "tableName": "CLIPRSLT"}, {"tabCode": "SECTRSLT_6", "mqMessageId": "6", "msgType": "Binary", "tableName": "SECTRSLT"}, {"tabCode": "TRCKRSLT_7", "mqMessageId": "7", "msgType": "Binary", "tableName": "TRCKRSLT"}, {"tabCode": "BANDRSLT_8", "mqMessageId": "8", "msgType": "Binary", "tableName": "BANDRSLT"}, {"tabCode": "HEADLOG_10", "mqMessageId": "10", "msgType": "Binary", "tableName": "HEADLOG"}, {"tabCode": "BATCHINFO_11", "mqMessageId": "11", "msgType": "Binary", "tableName": "BATCHINFO"}, {"tabCode": "TLOG_12", "mqMessageId": "12", "msgType": "Binary", "tableName": "TLOG"}, {"tabCode": "MONDATA_15", "mqMessageId": "15", "msgType": "Binary", "tableName": "MONDATA"}, {"tabCode": "GMONDATA_18", "mqMessageId": "18", "msgType": "Binary", "tableName": "GMONDATA"}, {"tabCode": "MONRSLT_19", "mqMessageId": "19", "msgType": "Binary", "tableName": "MONRSLT"}, {"tabCode": "GAUGERUN_20", "mqMessageId": "20", "msgType": "Binary", "tableName": "GAUGERUN"}, {"tabCode": "SH_GMONDATA_21", "mqMessageId": "21", "msgType": "Binary", "tableName": "SH_GMONDATA"}, {"tabCode": "MISCDATA_24", "mqMessageId": "24", "msgType": "Binary", "tableName": "MISCDATA"}, {"tabCode": "GEOMRSLT_25", "mqMessageId": "25", "msgType": "Binary", "tableName": "GEOMRSLT"}, {"tabCode": "DECOGAUGE_26", "mqMessageId": "26", "msgType": "Binary", "tableName": "DECOGAUGE"}, {"tabCode": "BANDRSLT_STRING_27", "mqMessageId": "27", "msgType": "Binary", "tableName": "BANDRSLT_STRING", "splitter": "$"}, {"tabCode": "GLDLOG_29", "mqMessageId": "29", "msgType": "Binary", "tableName": "GLDLOG"}, {"tabCode": "TESTLOG_30", "mqMessageId": "30", "msgType": "Binary", "tableName": "TESTLOG"}, {"tabCode": "SOFTRSLT_31", "mqMessageId": "31", "msgType": "Binary", "tableName": "SOFTRSLT"}, {"tabCode": "MONRSLT_32", "mqMessageId": "32", "msgType": "Binary", "tableName": "MONRSLT"}, {"tabCode": "GAUGERUN_33", "mqMessageId": "33", "msgType": "Binary", "tableName": "GAUGERUN"}, {"tabCode": "GLDLOG_34", "mqMessageId": "34", "msgType": "Binary", "tableName": "GLDLOG"}, {"tabCode": "TESTLOG_35", "mqMessageId": "35", "msgType": "Binary", "tableName": "TESTLOG"}, {"tabCode": "TRCKRSLT_36", "mqMessageId": "36", "msgType": "Binary", "tableName": "TRCKRSLT"}, {"tabCode": "GLDPARM_37", "mqMessageId": "37", "msgType": "Binary", "tableName": "GLDPARM"}, {"tabCode": "MAGCBPRM_38", "mqMessageId": "38", "msgType": "Binary", "tableName": "MAGCBPRM"}, {"tabCode": "MAGMBPRM_39", "mqMessageId": "39", "msgType": "Binary", "tableName": "MAGMBPRM"}, {"tabCode": "SCNRINFO_40", "mqMessageId": "40", "msgType": "Binary", "tableName": "SCNRINFO"}, {"tabCode": "GLDLOG_41", "mqMessageId": "41", "msgType": "Binary", "tableName": "GLDLOG"}, {"tabCode": "TESTLOG_42", "mqMessageId": "42", "msgType": "Binary", "tableName": "TESTLOG"}, {"tabCode": "HEADLOG_43", "mqMessageId": "43", "msgType": "Binary", "tableName": "HEADLOG"}, {"tabCode": "GMONDATA_44", "mqMessageId": "44", "msgType": "Binary", "tableName": "GMONDATA"}, {"tabCode": "SH_GMONDATA_45", "mqMessageId": "45", "msgType": "Binary", "tableName": "SH_GMONDATA"}]}